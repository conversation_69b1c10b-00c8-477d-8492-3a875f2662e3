<?php

require_once 'vendor/autoload.php';

use Illuminate\Http\Request;
use App\Http\Controllers\SuperadminController;
use Carbon\Carbon;

// Set up Laravel environment
$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "Testing Division Parts Detail API Fix\n";
echo "=====================================\n\n";

// Test 1: Check if we have any unit transactions with parts
echo "1. Checking unit transaction data...\n";

$recentTransactions = DB::table('unit_transaction_parts')
    ->join('unit_transactions', 'unit_transaction_parts.unit_transaction_id', '=', 'unit_transactions.id')
    ->join('part_inventories', 'unit_transaction_parts.part_inventory_id', '=', 'part_inventories.part_inventory_id')
    ->join('parts', 'part_inventories.part_code', '=', 'parts.part_code')
    ->where('unit_transactions.created_at', '>=', Carbon::now()->subMonths(3)->startOfMonth())
    ->select('parts.part_type', DB::raw('COUNT(*) as count'))
    ->groupBy('parts.part_type')
    ->get();

echo "   Recent transactions by division (last 3 months):\n";
foreach ($recentTransactions as $transaction) {
    echo "   - {$transaction->part_type}: {$transaction->count} transactions\n";
}

if ($recentTransactions->isEmpty()) {
    echo "   No recent transactions found. Testing with current month anyway...\n";
}

echo "\n";

// Test 2: Test the API endpoint with AC division
echo "2. Testing API endpoint with AC division...\n";

$controller = new SuperadminController();
$request = new Request([
    'division' => 'AC',
    'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
    'end_date' => Carbon::now()->format('Y-m-d')
]);

try {
    $response = $controller->getDivisionPartsDetail($request);
    $data = json_decode($response->getContent(), true);

    echo "   API Response Status: " . $response->getStatusCode() . "\n";
    echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "   Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";

    if (isset($data['summary'])) {
        echo "   Summary:\n";
        echo "     - Total Items: " . $data['summary']['total_items'] . "\n";
        echo "     - Total Quantity: " . $data['summary']['total_quantity'] . "\n";
        echo "     - Total Value: Rp " . number_format($data['summary']['total_value'], 0, ',', '.') . "\n";
        echo "     - Total Value with PPN: Rp " . number_format($data['summary']['total_value_with_ppn'], 0, ',', '.') . "\n";
    }

    if (isset($data['data']) && count($data['data']) > 0) {
        echo "   Sample transaction data:\n";
        $sample = $data['data'][0];
        echo "     - Part: {$sample['part_name']} ({$sample['part_code']})\n";
        echo "     - Site: {$sample['site_name']}\n";
        echo "     - Quantity: {$sample['quantity']}\n";
        echo "     - Price: Rp " . number_format($sample['price'], 0, ',', '.') . "\n";
        echo "     - Date: {$sample['transaction_date']}\n";
        echo "     - WO: {$sample['wo_number']}\n";
        echo "     - Status: {$sample['transaction_status']}\n";
    }

} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 3: Test with TYRE division
echo "3. Testing API endpoint with TYRE division...\n";

$request = new Request([
    'division' => 'TYRE',
    'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
    'end_date' => Carbon::now()->format('Y-m-d')
]);

try {
    $response = $controller->getDivisionPartsDetail($request);
    $data = json_decode($response->getContent(), true);

    echo "   API Response Status: " . $response->getStatusCode() . "\n";
    echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "   Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";

} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Test with FABRIKASI division
echo "4. Testing API endpoint with FABRIKASI division...\n";

$request = new Request([
    'division' => 'FABRIKASI',
    'start_date' => Carbon::now()->startOfMonth()->format('Y-m-d'),
    'end_date' => Carbon::now()->format('Y-m-d')
]);

try {
    $response = $controller->getDivisionPartsDetail($request);
    $data = json_decode($response->getContent(), true);

    echo "   API Response Status: " . $response->getStatusCode() . "\n";
    echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
    echo "   Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";

} catch (Exception $e) {
    echo "   Error: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 5: Test with broader date range if current month has no data
if ($recentTransactions->isEmpty() || $recentTransactions->sum('count') == 0) {
    echo "5. Testing with broader date range (last 6 months)...\n";

    $request = new Request([
        'division' => 'AC',
        'start_date' => Carbon::now()->subMonths(6)->startOfMonth()->format('Y-m-d'),
        'end_date' => Carbon::now()->format('Y-m-d')
    ]);

    try {
        $response = $controller->getDivisionPartsDetail($request);
        $data = json_decode($response->getContent(), true);

        echo "   API Response Status: " . $response->getStatusCode() . "\n";
        echo "   Success: " . ($data['success'] ? 'true' : 'false') . "\n";
        echo "   Data count: " . (isset($data['data']) ? count($data['data']) : 0) . "\n";

    } catch (Exception $e) {
        echo "   Error: " . $e->getMessage() . "\n";
    }
}

echo "\n";
echo "Test completed. The modal should now show actual transaction data instead of inventory data.\n";
echo "Check the Laravel logs for detailed query information: storage/logs/laravel.log\n";
